/// PumpFun CPI解析器核心实现
///
/// 提供完整的PumpFun交易解析功能，包括指令解析和CPI事件解析

use crate::plugins::pump::{
    models::instructions::PumpProgramIx,
    models::events::{TradeEvent, TradeEventEvent, TradeEventExtended},
    types::{PumpTransactionType, SwapDirection, TransactionDetails, TokenInfo, PriceInfo, FeeInfo},
    types::constants::*,
};
use crate::core::types::get_config;
use anyhow::Result;
use base64::{engine::general_purpose::STANDARD, Engine as _};

use chrono::{DateTime, Utc};
use log::{debug, warn};
use rust_decimal::Decimal;
use solana_program::pubkey::Pubkey;
use std::str::FromStr;
use thiserror::Error;
use std::time::Instant;

/// PumpFun解析错误类型
#[derive(Error, Debug)]
pub enum PumpParseError {
    #[error("指令反序列化失败: {0}")]
    InstructionDeserializationError(String),
    #[error("事件解析失败: {0}")]
    EventParseError(String),
    #[error("无效的程序ID: {0}")]
    InvalidProgramId(String),
    #[error("缺少必要数据: {0}")]
    MissingRequiredData(String),
    #[error("数据格式错误: {0}")]
    InvalidDataFormat(String),
}

/// 解析后的PumpFun交易数据
#[derive(Debug, Clone)]
pub struct ParsedPumpTransaction {
    /// 基础交易详情
    pub details: TransactionDetails,
    /// 原始指令数据
    pub raw_instruction: Option<Vec<u8>>,
    /// 原始事件数据
    pub raw_event: Option<Vec<u8>>,
    /// 解析的指令
    pub parsed_instruction: Option<PumpProgramIx>,
    /// 解析的事件
    pub parsed_event: Option<TradeEventEvent>,
    /// 解析耗时（微秒）
    pub parse_duration_micros: u128,
}

/// PumpFun解析器
pub struct PumpParser;

impl PumpParser {
    /// 创建新的解析器实例
    pub fn new() -> Self {
        Self
    }

    /// 计算创作者金库地址 (PDA)
    /// 
    /// 根据PumpFun协议规范，使用创作者地址和"creator_vault"种子计算PDA
    pub fn calculate_creator_vault_address(&self, creator: &Pubkey) -> Result<Pubkey, PumpParseError> {
        // PumpFun程序ID
        let program_id = Pubkey::from_str(&get_config().programs.pump_program)
            .map_err(|e| PumpParseError::InvalidProgramId(e.to_string()))?;
        
        // 计算PDA: seeds = ["creator-vault", creator.as_ref()]
        let creator_vault_seed = b"creator-vault";
        let seeds = &[creator_vault_seed, creator.as_ref()];
        
        let (creator_vault_pda, _bump) = Pubkey::find_program_address(seeds, &program_id);
        
        debug!("计算创作者金库地址: 创作者={}, 金库={}", creator, creator_vault_pda);
        
        Ok(creator_vault_pda)
    }

    /// 创建扩展的交易事件，包含计算的creator_vault地址和当前价格
    pub fn create_extended_trade_event(&self, event: TradeEvent, signature: Option<String>) -> Result<TradeEventExtended, PumpParseError> {
        let creator_vault = self.calculate_creator_vault_address(&event.creator)?;
        
        // 计算当前价格: virtual_sol_reserves / virtual_token_reserves
        let current_price = if event.virtual_token_reserves > 0 {
            let sol_amount = event.virtual_sol_reserves as f64 / LAMPORTS_PER_SOL as f64;
            let token_amount = event.virtual_token_reserves as f64 / TOKENS_PER_UNIT as f64;
            
            if token_amount > 0.0 {
                Some(sol_amount / token_amount)
            } else {
                None
            }
        } else {
            None
        };
        
        if let Some(price) = current_price {
            debug!("计算当前价格: {} SOL/Token (虚拟SOL储备: {}, 虚拟Token储备: {})", 
                price, event.virtual_sol_reserves, event.virtual_token_reserves);
        }
        
        Ok(TradeEventExtended {
            event,
            creator_vault,
            signature,
            current_price,
        })
    }

    /// 解析PumpFun指令数据
    pub fn parse_instruction(&self, instruction_data: &[u8]) -> Result<PumpProgramIx, PumpParseError> {
        PumpProgramIx::deserialize(instruction_data)
            .map_err(|e| PumpParseError::InstructionDeserializationError(e.to_string()))
    }

    /// 从交易日志中解析CPI事件，并返回解析耗时和错误
    pub fn parse_cpi_events_from_logs(&self, log_messages: &[String]) -> (Vec<TradeEventEvent>, Vec<String>) {
        let start_time = Instant::now();
        let mut events = Vec::new();
        let mut errors = Vec::new();
        let mut error_types = std::collections::HashMap::new();
        
        for (index, log_message) in log_messages.iter().enumerate() {
            // 标准化日志消息，避免因特殊字符或空格导致解析失败
            let trimmed_log = log_message.trim();
            
            if trimmed_log.starts_with(PROGRAM_DATA_PREFIX) {
                let data_part = trimmed_log.trim_start_matches(PROGRAM_DATA_PREFIX).trim();
                
                // 尝试解析TradeEvent事件
                match TradeEventEvent::from_base64(data_part) {
                    Ok(trade_event) => {
                        events.push(trade_event);
                    }
                    Err(e) => {
                        // 简化的错误消息，仅保留错误类型，不显示详细错误
                        let error_type = format!("交易事件判别符不匹配");
                        let entry = error_types.entry(error_type.clone()).or_insert(0);
                        *entry += 1;
                        errors.push(error_type);
                    }
                }
            }
        }
        
        // 只打印一次汇总日志，而不是每个错误都打印
        for (error_type, count) in error_types {
            if count > 0 {
                debug!("Pump解析错误: {} (共 {} 处)", error_type, count);
            }
        }
        
        let elapsed = start_time.elapsed();
        
        if events.is_empty() && errors.is_empty() {
            debug!("未发现PumpFun事件");
        }
        
        (events, errors)
    }

    /// 解析完整的PumpFun交易
    pub fn parse_transaction(
        &self,
        signature: &str,
        instruction_data: Option<&[u8]>,
        log_messages: &[String],
        accounts: &[Pubkey],
    ) -> Result<ParsedPumpTransaction> {
        let start_time = Instant::now();
        
        let mut parsed_tx = ParsedPumpTransaction {
            details: TransactionDetails {
                signature: signature.to_string(),
                timestamp: Some(Utc::now()),
                ..Default::default()
            },
            raw_instruction: instruction_data.map(|d| d.to_vec()),
            raw_event: None,
            parsed_instruction: None,
            parsed_event: None,
            parse_duration_micros: 0,
        };

        // 解析指令
        if let Some(data) = instruction_data {
            match self.parse_instruction(data) {
                Ok(instruction) => {
                    debug!("成功解析指令: {}", instruction.name());
                    parsed_tx.details.transaction_type = PumpTransactionType::from(instruction.name().as_str());
                    parsed_tx.parsed_instruction = Some(instruction.clone());
                    
                    // 根据指令类型提取基础信息
                    match instruction {
                        PumpProgramIx::Buy(buy_args) => {
                            // Buy 指令
                            parsed_tx.details.swap_direction = Some(SwapDirection::Buy);
                            parsed_tx.details.token_amount = Some(buy_args.amount.into());
                            parsed_tx.details.sol_amount = Some(buy_args.max_sol_cost.into());
                            debug!("解析Buy指令: 代币={}, SOL={}", buy_args.amount, buy_args.max_sol_cost);
                        }
                        PumpProgramIx::Sell(sell_args) => {
                            // Sell 指令
                            parsed_tx.details.swap_direction = Some(SwapDirection::Sell);
                            parsed_tx.details.token_amount = Some(sell_args.amount.into());
                            parsed_tx.details.sol_amount = Some(sell_args.min_sol_output.into());
                        }
                        PumpProgramIx::Create(create_args) => {
                            if let Some(mint) = accounts.get(0) {
                                parsed_tx.details.token_info = Some(TokenInfo {
                                    mint: *mint,
                                    name: Some(create_args.name),
                                    symbol: Some(create_args.symbol),
                                    uri: Some(create_args.uri),
                                    decimals: 6, // PumpFun默认6位小数
                                    total_supply: None,
                                });
                            }
                        }
                        _ => {}
                    }
                }
                Err(e) => {
                    debug!("指令解析失败: {} - 签名: {}", e.to_string().split(':').next().unwrap_or("未知错误"), signature);
                }
            }
        }

        // 解析CPI事件
        let (events, errors) = self.parse_cpi_events_from_logs(log_messages);
        
        // 只打印一次签名和错误计数，而不是每个错误都打印
        if !errors.is_empty() {
            debug!("交易 {} 事件解析失败 ({} 个错误)", signature, errors.len());
        }
        
        if let Some(trade_event) = events.first() {
            parsed_tx.parsed_event = Some(trade_event.clone());
            parsed_tx.raw_event = None; // 事件原始数据暂不保存
            
            // 从事件中提取详细信息
            self.extract_details_from_event(&mut parsed_tx.details, &trade_event.0);
        }

        // 设置完成状态
        parsed_tx.details.is_complete = parsed_tx.parsed_instruction.is_some() || parsed_tx.parsed_event.is_some();
        
        // 记录解析耗时
        parsed_tx.parse_duration_micros = start_time.elapsed().as_micros();

        Ok(parsed_tx)
    }

    /// 从TradeEvent中提取交易详情
    fn extract_details_from_event(&self, details: &mut TransactionDetails, event: &TradeEvent) {
        // 设置金额
        details.sol_amount = Some(event.sol_amount.into());
        details.token_amount = Some(event.token_amount.into());
        
        // 计算价格
        let current_price = if event.token_amount > 0 {
            Decimal::from(event.sol_amount) / Decimal::from(event.token_amount)
        } else {
            Decimal::ZERO
        };

        // 计算创作者金库地址
        let creator_vault = self.calculate_creator_vault_address(&event.creator).ok();
        
        // 设置价格信息
        details.price_info = Some(PriceInfo {
            token_price: current_price,
            amount_sol: Decimal::from(event.sol_amount),
            amount_token: Decimal::from(event.token_amount),
            current_supply: Some(event.virtual_token_reserves), // 使用虚拟代币储备作为当前供应量
        });
        
        // 设置费用信息
        details.fee_info = Some(FeeInfo {
            fee_recipient: Some(event.fee_recipient),
            fee_basis_points: Some(event.fee_basis_points as u16),
            fee_amount: Some(event.fee.into()),
            creator_fee_recipient: Some(event.creator),
            creator_fee_basis_points: Some(event.creator_fee_basis_points as u16),
            creator_fee_amount: Some(event.creator_fee.into()),
            creator_vault,
        });

        // 时间戳
        if event.timestamp > 0 {
            details.timestamp = DateTime::from_timestamp(event.timestamp as i64, 0);
        }
    }

    /// 验证是否为PumpFun程序交易
    pub fn is_pump_transaction(&self, program_id: &str) -> bool {
        program_id == get_config().programs.pump_program
    }

    /// 从base64编码的指令数据解析
    pub fn parse_instruction_from_base64(&self, data: &str) -> Result<PumpProgramIx, PumpParseError> {
        let decoded = STANDARD.decode(data)
            .map_err(|e| PumpParseError::InvalidDataFormat(e.to_string()))?;
        
        self.parse_instruction(&decoded)
    }
}

impl Default for PumpParser {
    fn default() -> Self {
        Self::new()
    }
}

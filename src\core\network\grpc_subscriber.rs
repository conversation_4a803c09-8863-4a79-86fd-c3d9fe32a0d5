/// gRPC订阅者
/// 
/// 连接Solana gRPC端点，订阅交易数据流

use log::{info, error};
use anyhow::{Result, anyhow};
use yellowstone_grpc_client::GeyserGrpcClient;
use yellowstone_grpc_proto::prelude::*;
use yellowstone_grpc_proto::prelude::subscribe_update::UpdateOneof;
use tokio::sync::mpsc;
use tokio_stream::StreamExt;
use std::sync::Arc;
use std::collections::HashMap;
use std::time::Duration;

use crate::core::parsers::RawTransaction;

/// 零延迟gRPC订阅器
pub struct GrpcSubscriber {
    endpoint: String,
    accounts: Vec<String>,
    tx: mpsc::Sender<Arc<RawTransaction>>,
}

impl GrpcSubscriber {
    pub fn new(endpoint: String, accounts: Vec<String>, tx: mpsc::Sender<Arc<RawTransaction>>) -> Self {
        Self { endpoint, accounts, tx }
    }
    
    /// 连接到gRPC端点并订阅更新
    pub async fn subscribe(&self) -> Result<()> {
        info!("连接到 gRPC 端点: {}", self.endpoint);
        
        // 使用builder模式创建gRPC客户端
        let mut client = GeyserGrpcClient::build_from_shared(self.endpoint.clone())?
            .connect_timeout(Duration::from_secs(10))
            .timeout(Duration::from_secs(10))
            .tls_config(tonic::transport::channel::ClientTlsConfig::new().with_native_roots())?
            .max_decoding_message_size(1024 * 1024 * 1024)
            .connect()
            .await?;
        
        // 创建账户过滤器
        let mut accounts_filters = HashMap::new();
        for (i, account) in self.accounts.iter().enumerate() {
            accounts_filters.insert(
                format!("account-{}", i),
                SubscribeRequestFilterAccounts {
                    account: vec![account.clone()],
                    owner: vec!["".to_string()],
                    filters: vec![],
                    nonempty_txn_signature: Some(false),
                }
            );
        }
        
        // 创建请求对象
        let subscribe_request = SubscribeRequest {
            accounts: accounts_filters,
            slots: HashMap::new(),
            transactions: HashMap::new(),
            transactions_status: HashMap::new(),
            blocks: HashMap::new(),
            blocks_meta: HashMap::new(),
            entry: HashMap::new(),
            commitment: Some(1), // Confirmed
            accounts_data_slice: vec![],
            ping: None,
            from_slot: None,
        };
        
        // 创建订阅
        let mut stream = client.subscribe_once(subscribe_request).await?;
        
        info!("成功订阅 gRPC 端点");
        
        // 处理流数据
        while let Some(message) = stream.next().await {
            match message {
                Ok(update) => {
                    if let Some(UpdateOneof::Transaction(tx)) = update.update_oneof {
                        // 使用 from_update 创建 RawTransaction
                        if let Some(raw_tx) = RawTransaction::from_update(tx) {
                            // 发送到处理通道
                            if let Err(e) = self.tx.send(Arc::new(raw_tx)).await {
                                error!("发送交易数据失败: {}", e);
                            }
                        }
                    }
                },
                Err(e) => {
                    error!("gRPC 流错误: {}", e);
                }
            }
        }
        
        Err(anyhow!("gRPC 流已关闭"))
    }
}
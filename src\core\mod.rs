pub mod network;
pub mod processing;
pub mod storage;
pub mod types;
pub mod parsers;
pub mod stats;

// 重新导出核心类型，解决命名冲突
pub use types::{
    PumpTransactionType, SwapDirection, TokenInfo, PriceInfo, 
    PipelineConfig, RealTimeMetrics, FeeInfo, TransactionDetails, 
    ParsedEvent, get_config
};
pub use processing::*;
pub use network::*;
// 导出插件统计功能
pub use stats::*;
// 只导出 parsers 模块中的特定类型和 trait
pub use parsers::{TransactionParser, ParserFactory};

// 获取PumpFun程序ID的辅助函数
#[inline]
pub fn get_pump_program_id() -> &'static str {
    &get_config().programs.pump_program
}

// 不再使用硬编码常量
// pub const PUMP_PROGRAM_ID: &str = "PuMP8ypBCGyExYsmCnAw9GEVz16c7uPZjZLD3gZsUQc";
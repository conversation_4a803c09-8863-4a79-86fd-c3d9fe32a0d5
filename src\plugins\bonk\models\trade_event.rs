use serde::{Serialize, Deserialize};

/// 池子状态
#[derive(Debug, Serialize, Deserialize, PartialEq, Eq, Clone)]
pub enum PoolStatus {
    Fund,
    Migrate,
    Trade,
}

impl std::fmt::Display for PoolStatus {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            PoolStatus::Fund => write!(f, "Fund"),
            PoolStatus::Migrate => write!(f, "Migrate"),
            PoolStatus::Trade => write!(f, "Trade"),
        }
    }
}

/// 交易方向
#[derive(Debug, Serialize, Deserialize, PartialEq, Eq, Clone)]
pub enum TradeDirection {
    Buy,
    Sell,
}

impl std::fmt::Display for TradeDirection {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            TradeDirection::Buy => write!(f, "买入"),
            TradeDirection::Sell => write!(f, "卖出"),
        }
    }
}

/// TradeEvent CPI数据结构，字段名称与Anchor结构一致
#[derive(Debug, Clone)]
pub struct CpiTradeEvent {
    pub pool_state: [u8; 32],
    pub total_base_sell: u64,
    pub virtual_base: u64,
    pub virtual_quote: u64,
    pub real_base_before: u64,
    pub real_quote_before: u64,
    pub real_base_after: u64,
    pub real_quote_after: u64,
    pub amount_in: u64,
    pub amount_out: u64,
    pub protocol_fee: u64,
    pub platform_fee: u64,
    pub share_fee: u64,
    pub trade_direction: u8,
    pub pool_status: u8,
}

impl CpiTradeEvent {
    /// TradeEvent的anchor discriminator (完整16字节)
    pub const DISCRIMINATOR: [u8; 16] = [
        0xe4, 0x45, 0xa5, 0x2e, 0x51, 0xcb, 0x9a, 0x1d,
        0xbd, 0xdb, 0x7f, 0xd3, 0x4e, 0xe6, 0x61, 0xee
    ];
    
    /// 从字节数据解析TradeEvent
    pub fn from_bytes(data: &[u8]) -> Option<Self> {
        if data.len() < 16 {
            return None;
        }

        // 检查discriminator (现在是16字节)
        let discriminator = &data[0..16];
        if discriminator != Self::DISCRIMINATOR {
            return None;
        }
        
        // 打印整个数据的十六进制表示，便于调试 (仅在debug模式)
        #[cfg(debug_assertions)]
        {
            let hex_data: Vec<String> = data.iter().map(|b| format!("{:02x}", b)).collect();
            log::debug!("完整数据十六进制: [{}]", hex_data.join(", "));
        }
        
        let required_len = 16 + 32 + 8 * 11 + 2; // discriminator(16) + pubkey + 11个u64 + 2个u8
        if data.len() < required_len {
            return None;
        }
        
        // 从原始字节开始重新解析
        let mut offset = 16; // 跳过16字节discriminator
        
        // 定义一个高效的u64解析函数(减少日志开销)
        let parse_u64_fast = |data: &[u8], offset: usize| -> u64 {
            let bytes = &data[offset..offset + 8];
            u64::from_le_bytes(bytes.try_into().unwrap())
        };
        
        // 1. 解析pool_state (32字节) - 移除调试日志
        let mut pool_state = [0u8; 32];
        pool_state.copy_from_slice(&data[offset..offset + 32]);
        offset += 32;
        
        // 2. 快速解析所有u64字段
        let total_base_sell = parse_u64_fast(data, offset); offset += 8;
        let virtual_base = parse_u64_fast(data, offset); offset += 8;
        let virtual_quote = parse_u64_fast(data, offset); offset += 8;
        let real_base_before = parse_u64_fast(data, offset); offset += 8;
        let real_quote_before = parse_u64_fast(data, offset); offset += 8;
        let real_base_after = parse_u64_fast(data, offset); offset += 8;
        let real_quote_after = parse_u64_fast(data, offset); offset += 8;
        let amount_in = parse_u64_fast(data, offset); offset += 8;
        let amount_out = parse_u64_fast(data, offset); offset += 8;
        let protocol_fee = parse_u64_fast(data, offset); offset += 8;
        let platform_fee = parse_u64_fast(data, offset); offset += 8;
        let share_fee = parse_u64_fast(data, offset); offset += 8;
        
        // 3. 解析枚举字段(移除调试日志)
        let trade_direction = data[offset]; offset += 1;
        let pool_status = data[offset];
        
        // 创建CpiTradeEvent对象(移除调试日志)
        Some(CpiTradeEvent {
            pool_state,
            total_base_sell,
            virtual_base,
            virtual_quote,
            real_base_before,
            real_quote_before,
            real_base_after,
            real_quote_after,
            amount_in,
            amount_out,
            protocol_fee,
            platform_fee,
            share_fee,
            trade_direction,
            pool_status,
        })
    }
}

/// 格式化的交易事件数据
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct LogTradeEvent {
    pub signature: String,
    pub pool_state: String,
    pub signer: String,
    pub mint_address: String,
    pub total_base_sell: u64,
    pub virtual_base: u64,
    pub virtual_quote: u64,
    pub real_base_before: u64,
    pub real_quote_before: u64,
    pub real_base_after: u64,
    pub real_quote_after: u64,
    pub amount_in: u64,
    pub amount_out: u64,
    pub protocol_fee: u64,
    pub platform_fee: u64,
    pub share_fee: u64,
    pub trade_direction: TradeDirection,
    pub pool_status: PoolStatus,
    pub price_before: f64,
    pub price_after: f64,
    pub slippage: f64,
    pub pool_base_vault: String,
    pub pool_quote_vault: String,
}

impl LogTradeEvent {
    /// 计算买入前价格: real_quote_before(精度9) ÷ real_base_before(精度6)
    pub fn calculate_price_before(real_base_before: u64, real_quote_before: u64) -> f64 {
        if real_base_before == 0 {
            return 0.0;
        }
        
        let quote_factor = 10_f64.powi(9); // real_quote_before 精度9
        let base_factor = 10_f64.powi(6);  // real_base_before 精度6
        
        let quote_adjusted = real_quote_before as f64 / quote_factor;
        let base_adjusted = real_base_before as f64 / base_factor;
        
        quote_adjusted / base_adjusted
    }
    
    /// 计算买入后价格: real_quote_after(精度9) ÷ real_base_after(精度6)
    pub fn calculate_price_after(real_base_after: u64, real_quote_after: u64) -> f64 {
        if real_base_after == 0 {
            return 0.0;
        }
        
        let quote_factor = 10_f64.powi(9); // real_quote_after 精度9
        let base_factor = 10_f64.powi(6);  // real_base_after 精度6
        
        let quote_adjusted = real_quote_after as f64 / quote_factor;
        let base_adjusted = real_base_after as f64 / base_factor;
        
        quote_adjusted / base_adjusted
    }
    
    /// 计算滑点: (price_after - price_before) / price_before * 100
    pub fn calculate_slippage(price_before: f64, price_after: f64) -> f64 {
        if price_before == 0.0 {
            return 0.0;
        }
        
        ((price_after - price_before) / price_before) * 100.0
    }
} 